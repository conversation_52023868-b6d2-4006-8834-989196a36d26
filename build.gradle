plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

// Common configuration for all projects
allprojects {
    group = 'com.nttdata.ndvn'
    version = '1.0.0-SNAPSHOT'
    
    repositories {
        mavenCentral()
    }
}

// Common configuration for all subprojects
subprojects {
    apply plugin: 'java'
    apply plugin: 'io.spring.dependency-management'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    dependencyManagement {
        imports {
            // Import Terasoluna parent BOM
            mavenBom 'org.terasoluna.gfw:terasoluna-gfw-parent:5.10.0.RELEASE'
        }
    }
    
    dependencies {
        // Common dependencies for all modules
        implementation 'org.springframework:spring-context'
        implementation 'org.springframework:spring-core'
        implementation 'jakarta.annotation:jakarta.annotation-api'
        
        // Logging
        implementation 'org.slf4j:slf4j-api'
        implementation 'ch.qos.logback:logback-classic'
        
        // Test dependencies
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.junit.jupiter:junit-jupiter'
        testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    }
    
    test {
        useJUnitPlatform()
    }
    
    compileJava {
        options.encoding = 'UTF-8'
        options.compilerArgs += ['-parameters']
    }
    
    compileTestJava {
        options.encoding = 'UTF-8'
    }
}

// Root project specific configuration
repositories {
    mavenCentral()
}

// Wrapper configuration
wrapper {
    gradleVersion = '8.5'
    distributionType = Wrapper.DistributionType.ALL
}
