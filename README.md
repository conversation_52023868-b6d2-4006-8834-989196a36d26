# NDVN Terasoluna Base Project

A multi-module Gradle project using the Terasoluna framework version 5.10.0.RELEASE, designed as a backend-only codebase with proper module separation following Domain-Driven Design principles.

## Project Overview

This project serves as a foundation for building enterprise-grade backend applications using:
- **Terasoluna Framework 5.10.0.RELEASE** (org.terasoluna.gfw)
- **Spring Boot 3.4.1** (fully compatible)
- **Spring Framework 6.2.1**
- **Java 17+** with Jakarta EE 10
- **Java Configuration Only** (no XML configuration)

## Module Structure

The project follows a clean architecture with the following modules:

```
ndvn-terasoluna-base/
├── ndvn-terasoluna-base-domain/          # Domain layer
├── ndvn-terasoluna-base-infrastructure/  # Infrastructure layer
├── ndvn-terasoluna-base-application/     # Application layer
└── ndvn-terasoluna-base-web/            # Web layer (REST API)
```

### Module Dependencies

```
Web Layer
    ↓
Application Layer
    ↓
Infrastructure Layer
    ↓
Domain Layer
```

## Module Details

### 1. Domain Module (`ndvn-terasoluna-base-domain`)
- **Purpose**: Contains core business logic and domain entities
- **Dependencies**: Terasoluna common dependencies, Bean Validation
- **Packages**:
  - `com.nttdata.ndvn.domain.model` - Domain entities and value objects
  - `com.nttdata.ndvn.domain.repository` - Repository interfaces
  - `com.nttdata.ndvn.domain.service` - Domain services

### 2. Infrastructure Module (`ndvn-terasoluna-base-infrastructure`)
- **Purpose**: Implements data access and external integrations
- **Dependencies**: Domain module, JPA/MyBatis, database drivers
- **Packages**:
  - `com.nttdata.ndvn.infrastructure.repository` - Repository implementations
  - `com.nttdata.ndvn.infrastructure.config` - Infrastructure configuration

### 3. Application Module (`ndvn-terasoluna-base-application`)
- **Purpose**: Orchestrates business operations and use cases
- **Dependencies**: Domain and Infrastructure modules, MapStruct
- **Packages**:
  - `com.nttdata.ndvn.application.service` - Application services
  - `com.nttdata.ndvn.application.dto` - Data Transfer Objects

### 4. Web Module (`ndvn-terasoluna-base-web`)
- **Purpose**: Provides REST API endpoints and web configuration
- **Dependencies**: Application module, Spring Boot Web
- **Packages**:
  - `com.nttdata.ndvn.web.controller` - REST controllers
  - `com.nttdata.ndvn.web.config` - Web configuration

## Technology Stack

| Component | Technology | Version |
|-----------|------------|---------|
| Framework | Terasoluna GFW | 5.10.0.RELEASE |
| Spring Boot | Spring Boot | 3.4.1 |
| Spring Framework | Spring Framework | 6.2.1 |
| Java | OpenJDK | 17+ |
| Build Tool | Gradle | 8.5 |
| ORM | JPA/Hibernate | 6.6.4.Final |
| Database | H2 (dev), PostgreSQL (prod) | Latest |
| Validation | Bean Validation | 3.0 |
| Logging | SLF4J + Logback | Latest |
| JSON | Jackson | 2.18.2 |

## Getting Started

### Prerequisites
- Java 17 or higher
- Gradle 8.5+ (or use the included wrapper)

### Building the Project
```bash
# Using Gradle wrapper (recommended)
./gradlew build

# Or using system Gradle
gradle build
```

### Running the Application
```bash
# Run the web module
./gradlew :ndvn-terasoluna-base-web:bootRun

# Or run with specific profile
./gradlew :ndvn-terasoluna-base-web:bootRun --args='--spring.profiles.active=development'
```

### Testing the Application
```bash
# Run all tests
./gradlew test

# Run tests for specific module
./gradlew :ndvn-terasoluna-base-domain:test
```

## Configuration

### Environment Profiles
- **development**: H2 in-memory database, debug logging
- **production**: PostgreSQL database, optimized logging

### Application Properties
Configuration files are located in `ndvn-terasoluna-base-web/src/main/resources/`:
- `application.yml` - Default configuration
- `application-development.yml` - Development environment
- `application-production.yml` - Production environment

## API Endpoints

### Health Check
- **GET** `/api/health` - Application health status

Example response:
```json
{
  "status": "UP",
  "timestamp": "2024-01-15T10:30:00",
  "application": "ndvn-terasoluna-base",
  "version": "1.0.0-SNAPSHOT"
}
```

## Development Guidelines

### Java Configuration Approach
This project uses **Java-based configuration only**:
- `@Configuration` classes instead of XML files
- `@ComponentScan` for component discovery
- `@Import` for configuration composition

### Key Configuration Classes
- `DomainConfig` - Domain layer configuration
- `InfrastructureConfig` - Data access configuration
- `ApplicationConfig` - Application services configuration
- `WebConfig` - Web layer configuration

### Spring Boot Compatibility
The project is fully compatible with Spring Boot 3.4.1:
- Uses Spring Boot's dependency management
- Leverages Spring Boot auto-configuration where appropriate
- Maintains Terasoluna's architectural principles

## Database Configuration

### Development (H2)
```yaml
spring:
  datasource:
    url: jdbc:h2:mem:devdb
    username: sa
    password: 
  h2:
    console:
      enabled: true
      path: /h2-console
```

### Production (PostgreSQL)
```yaml
spring:
  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    driver-class-name: org.postgresql.Driver
```

## Next Steps

1. **Add Domain Entities**: Create your domain models in the domain module
2. **Implement Repositories**: Add repository implementations in the infrastructure module
3. **Create Application Services**: Build use cases in the application module
4. **Add REST Controllers**: Implement API endpoints in the web module
5. **Configure Security**: Add Spring Security configuration as needed
6. **Add Database Migrations**: Use Flyway or Liquibase for database versioning

## Support

For questions about Terasoluna framework, refer to:
- [Terasoluna Official Documentation](https://terasolunaorg.github.io/guideline/current/en/)
- [Spring Boot Documentation](https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/)

## Architecture Decisions

### Why Terasoluna 5.10.0.RELEASE?
- **Enterprise-grade**: Proven framework for large-scale applications
- **Spring Boot 3.4.1 Compatible**: Leverages latest Spring ecosystem
- **Jakarta EE 10**: Future-proof with modern Java standards
- **Java Configuration**: No XML configuration files needed

### Module Separation Benefits
- **Clear Boundaries**: Each layer has distinct responsibilities
- **Testability**: Easy to unit test individual layers
- **Maintainability**: Changes in one layer don't affect others
- **Scalability**: Modules can be deployed independently if needed

### Configuration Strategy
- **Java-based Configuration**: Type-safe, IDE-friendly
- **Profile-based**: Environment-specific configurations
- **Property Externalization**: 12-factor app compliance

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clean and rebuild
   ./gradlew clean build
   ```

2. **H2 Console Access**
   - URL: http://localhost:8080/h2-console
   - JDBC URL: jdbc:h2:mem:devdb
   - Username: sa
   - Password: (empty)

3. **Port Conflicts**
   ```yaml
   # Change port in application.yml
   server:
     port: 8081
   ```

## License

This project is licensed under the MIT License.
