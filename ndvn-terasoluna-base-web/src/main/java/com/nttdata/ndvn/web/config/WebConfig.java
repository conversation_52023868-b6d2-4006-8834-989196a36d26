package com.nttdata.ndvn.web.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web layer configuration class.
 * This configuration class is responsible for setting up web-specific beans
 * such as controllers, interceptors, and web security configurations.
 */
@Configuration
@EnableWebMvc
@ComponentScan(basePackages = {
    "com.nttdata.ndvn.web.controller"
})
public class WebConfig implements WebMvcConfigurer {
    
    // Web-specific configurations can be added here
    // For example: interceptors, view resolvers, etc.
}
