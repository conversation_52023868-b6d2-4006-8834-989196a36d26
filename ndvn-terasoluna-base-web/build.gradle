plugins {
    id 'org.springframework.boot'
}

dependencies {
    // Application module dependency
    implementation project(':ndvn-terasoluna-base-application')
    
    // Spring Boot starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // Terasoluna web dependencies
    implementation 'org.terasoluna.gfw:terasoluna-gfw-web-dependencies'
    implementation 'org.terasoluna.gfw:terasoluna-gfw-security-web-dependencies'
    
    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Development tools
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    
    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}
