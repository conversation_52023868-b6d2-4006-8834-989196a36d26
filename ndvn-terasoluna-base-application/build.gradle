dependencies {
    // Domain and Infrastructure module dependencies
    implementation project(':ndvn-terasoluna-base-domain')
    implementation project(':ndvn-terasoluna-base-infrastructure')
    
    // Terasoluna common dependencies
    implementation 'org.terasoluna.gfw:terasoluna-gfw-common-dependencies'
    
    // Spring Framework for application services
    implementation 'org.springframework:spring-context'
    implementation 'org.springframework:spring-tx'
    
    // Bean Validation
    implementation 'org.hibernate.validator:hibernate-validator'
    
    // Bean mapping utilities
    implementation 'org.mapstruct:mapstruct'
    annotationProcessor 'org.mapstruct:mapstruct-processor'
}
